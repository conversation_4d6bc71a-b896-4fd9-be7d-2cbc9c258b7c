package com.juzifenqi.plus.module.order.model.event.order;

import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.enums.CreateOrderFlagEnum;
import com.juzifenqi.plus.enums.OrderRenewEnum;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 会员订单创建参数
 *
 * <AUTHOR>
 */
@Data
public class PlusOrderCreateEvent {

    /**
     * 用户信息
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道标识
     */
    private Integer bizSource;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 购买上下文关联信息
     */
    private CreateOrderContext createOrderContext;

    /**
     * 开通方式，1-全款支付2-划扣3-后付款
     */
    private Integer payType;

    /**
     * 业务类型 1-联名卡订单
     */
    private Integer businessType;

    /**
     * 是否续费订单
     * <p>小额月卡和还款卡</p>
     *
     * @see OrderRenewEnum
     */
    private Integer renew;

    /**
     * 区分还款卡：首单/续费
     * <p>1=首单 2=续费单</p>
     *
     * @see CreateOrderFlagEnum
     */
    private Integer createFlag;

    /**
     * 连续包月价格
     */
    private BigDecimal renewPrice;

    /**
     * 三方订单号
     */
    private String outOrderSn;

    /**
     * 订单归因标记-可空
     */
    private String ascribeTo;

    /**
     * 创单场景入口标识  1=借款首页 2=确认借款页 3=信用支付完成页 4=落地页
     *
     * @see com.juzifenqi.plus.enums.CreateOrderSceneEnum
     */
    private Integer sceneCode;

    /**
     * 会员月卡第几期（1表示第1期，2表示第2期，以此类推）
     */
    private Integer monthPeriod;
}
