package com.juzifenqi.plus.module.order.repository.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员订单表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 15:33
 */
public class PlusOrderInfoPo {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单类型 1-开通2-续费3-升级4_初始化
     */
    private Integer orderType;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 方案名称
     */
    private String programName;

    /**
     * 订单状态 1_待支付;2_支付成功;3_取消
     */
    private Integer orderState;

    /**
     * 取消类型 1_有条件取消订单;2_无条件取消订单 3_急速退款-取消会员 4_过期未支付-系统取消 5_重复支付取消
     */
    private Integer cancelType;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     *
     */
    private BigDecimal orderAmount;

    /**
     *
     */
    private BigDecimal programPrice;

    /**
     *
     */
    private BigDecimal discountRate;

    /**
     * 订单回调时间
     */
    private Date callTime;

    /**
     * 支付成功时间
     */
    private Date payTime;

    /**
     *
     */
    private BigDecimal payAmount;

    /**
     * 支付方式 1-全款支付2-划扣3-后付款4-全款划扣5-首期支付
     */
    private Integer payType;

    /**
     * 首付金额（分期支付场景使用）
     */
    private BigDecimal firstPayAmount;

    /**
     *
     */
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 会员开始时间
     */
    private Date startTime;

    /**
     * 会员结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 渠道标识 1-宜口袋 2-桔多多
     */
    private Integer bizSource;

    /**
     * 会员月卡第几期（1表示第1期，2表示第2期，以此类推）
     */
    private Integer monthPeriod;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getProgramId() {
        return programId;
    }

    public void setProgramId(Integer programId) {
        this.programId = programId;
    }

    public String getProgramName() {
        return programName;
    }

    public void setProgramName(String programName) {
        this.programName = programName;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Integer getCancelType() {
        return cancelType;
    }

    public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getProgramPrice() {
        return programPrice;
    }

    public void setProgramPrice(BigDecimal programPrice) {
        this.programPrice = programPrice;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public BigDecimal getFirstPayAmount() {
        return firstPayAmount;
    }

    public void setFirstPayAmount(BigDecimal firstPayAmount) {
        this.firstPayAmount = firstPayAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBizSource() {
        return bizSource;
    }

    public void setBizSource(Integer bizSource) {
        this.bizSource = bizSource;
    }

    public Integer getMonthPeriod() {
        return monthPeriod;
    }

    public void setMonthPeriod(Integer monthPeriod) {
        this.monthPeriod = monthPeriod;
    }

}
