package com.juzifenqi.plus.module.order.repository.converter;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会员月卡续费计划转换器
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Mapper
public interface IPlusMonthMemberRenewalPlanRepositoryConverter {

    IPlusMonthMemberRenewalPlanRepositoryConverter instance = Mappers.getMapper(IPlusMonthMemberRenewalPlanRepositoryConverter.class);

    /**
     * Entity转Po
     *
     * @param entity 实体对象
     * @return Po对象
     */
    PlusMonthMemberRenewalPlanPo toPo(PlusMonthMemberRenewalPlanEntity entity);

    /**
     * Po转Entity
     *
     * @param po Po对象
     * @return 实体对象
     */
    PlusMonthMemberRenewalPlanEntity toEntity(PlusMonthMemberRenewalPlanPo po);

    /**
     * Entity列表转Po列表
     *
     * @param entities 实体对象列表
     * @return Po对象列表
     */
    List<PlusMonthMemberRenewalPlanPo> toPoList(List<PlusMonthMemberRenewalPlanEntity> entities);

    /**
     * Po列表转Entity列表
     *
     * @param pos Po对象列表
     * @return 实体对象列表
     */
    List<PlusMonthMemberRenewalPlanEntity> toEntityList(List<PlusMonthMemberRenewalPlanPo> pos);
}
