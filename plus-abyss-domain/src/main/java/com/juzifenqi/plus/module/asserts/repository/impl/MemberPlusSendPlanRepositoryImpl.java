package com.juzifenqi.plus.module.asserts.repository.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.dto.req.MemberPlusProfitSendPlanQueryReq;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusSendPlanStatusEnum;
import com.juzifenqi.plus.enums.PlusSendPlanTaskStatusEnum;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusCouponRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusSendPlanRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ExpireMemberPlusSendPlanConditionEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ExpireMemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanConditionEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanExtEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberPlusCouponRecordEntity;
import com.juzifenqi.plus.module.asserts.repository.converter.IMemberPlusSendPlanRepositoryConverter;
import com.juzifenqi.plus.module.asserts.repository.dao.IExpireIMemberPlusSendPlanMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanConditionMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanExtMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanTaskMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanConditionMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanExtMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanMapper;
import com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanTaskMapper;
import com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanConditionPo;
import com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanPo;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanExtPo;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanTaskPo;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberPlusSendPlanRepositoryImpl implements IMemberPlusSendPlanRepository {

    @Autowired
    private IMemberPlusSendPlanMapper                memberPlusSendPlanMapper;
    @Autowired
    private IMemberPlusSendPlanExtMapper             memberPlusSendPlanExtMapper;
    @Autowired
    private IMemberPlusSendPlanConditionMapper       memberPlusSendPlanConditionMapper;
    @Autowired
    private IMemberPlusCouponRepository              memberPlusCouponRepository;
    @Autowired
    private IMemberPlusSendPlanTaskMapper            memberPlusSendPlanTaskMapper;
    @Autowired
    private IExpireIMemberPlusSendPlanMapper         expireMemberPlusSendPlanMapper;
    @Autowired
    private IExpireMemberPlusSendPlanExtMapper       expireMemberPlusSendPlanExtMapper;
    @Autowired
    private IExpireMemberPlusSendPlanConditionMapper expireMemberPlusSendPlanConditionMapper;
    @Autowired
    private IExpireMemberPlusSendPlanTaskMapper      expireMemberPlusSendPlanTaskMapper;

    private final IMemberPlusSendPlanRepositoryConverter converter = IMemberPlusSendPlanRepositoryConverter.instance;

    /**
     * 保存发放计划
     */
    @Override
    public Integer save(MemberPlusSendPlanEntity memberPlusSendPlanEntity) {
        MemberPlusSendPlanPo memberPlusSendPlanPo = converter.toMemberPlusSendPlanPo(
                memberPlusSendPlanEntity);
        // 保存发放计划
        memberPlusSendPlanMapper.saveMemberPlusSendPlan(memberPlusSendPlanPo);
        Integer sendPlanId = memberPlusSendPlanPo.getId();
        // 保存扩展信息
        if (memberPlusSendPlanEntity.getExtEntity() != null) {
            MemberPlusSendPlanExtPo extPo = converter.toMemberPlusSendPlanExtPo(
                    memberPlusSendPlanEntity.getExtEntity(), sendPlanId);
            memberPlusSendPlanExtMapper.saveMemberPlusSendPlanExt(extPo);
        }
        // 保存条件
        if (!CollectionUtils.isEmpty(memberPlusSendPlanEntity.getPlusReachCondition())) {
            memberPlusSendPlanEntity.getPlusReachCondition().forEach(conditionEntity -> {
                MemberPlusSendPlanConditionPo conditionPo = converter.toMemberPlusSendPlanConditionPo(
                        conditionEntity, sendPlanId);
                memberPlusSendPlanConditionMapper.saveMemberPlusSendPlanCondition(conditionPo);
            });
        }
        // 保存发放计划任务
        if (memberPlusSendPlanEntity.getSendPlanTask() != null) {
            MemberPlusSendPlanTaskPo taskPo = converter.toMemberPlusSendPlanTaskPo(
                    memberPlusSendPlanEntity.getSendPlanTask(), sendPlanId);
            memberPlusSendPlanTaskMapper.saveMemberPlusSendPlanTask(taskPo);
        }
        return sendPlanId;
    }

    /**
     * 更新发放计划状态
     */
    @Override
    public void updateSendStatus(Integer id, Integer sendStatus) {
        memberPlusSendPlanMapper.updateSendStatus(id, sendStatus);
    }

    @Override
    public void invalidSendPlan(String orderSn) {
        log.info("失效发放计划开始：{}", orderSn);
        // 失效发放计划,排除结清返现
        List<MemberPlusSendPlanPo> list = memberPlusSendPlanMapper.listByOrderSnExcludeModelIds(
                orderSn, Collections.singletonList(PlusModelEnum.JQFX.getModelId()));
        if (CollectionUtils.isEmpty(list)) {
            log.info("失效发放计划,未获取到发放计划：{}", orderSn);
            return;
        }
        List<Integer> sendPlanIds = list.stream().map(MemberPlusSendPlanPo::getId)
                .collect(Collectors.toList());
        // 失效发放计划
        memberPlusSendPlanMapper.batchUpdateSendStatus(sendPlanIds,
                PlusSendPlanStatusEnum.INVALID.getValue());
        // 失效发放计划任务
        memberPlusSendPlanTaskMapper.batchUpdateSendStatus(sendPlanIds,
                PlusSendPlanTaskStatusEnum.ORDER_CANCEL.getValue());
        log.info("失效发放计划完成：{}", orderSn);
    }

    @Override
    public void invalidSendPlan(List<MemberPlusSendPlanEntity> list, String remark) {
        log.info("按发放计划失效开始：{}", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("按发放计划失效列表为空");
            return;
        }
        delSendPlanInfo(converter.toMemberPlusSendPlanPoList(list), remark);
        log.info("按发放计划失效完成");
    }

    /**
     * 获取订单的权益发放计划
     */
    @Override
    public List<MemberPlusSendPlanEntity> listByOrderSn(String orderSn) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listByOrderSn(
                orderSn);
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        return processSendPlanList(memberPlusSendPlanPos);
    }

    /**
     * 发放计划数据处理
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/2/29 15:00
     */
    private List<MemberPlusSendPlanEntity> processSendPlanList(
            List<MemberPlusSendPlanPo> memberPlusSendPlanPos) {
        // 达成条件
        Map<Integer, List<MemberPlusSendPlanConditionEntity>> conditionMap = getConditionMap(
                memberPlusSendPlanPos);
        // 扩展信息
        Map<Integer, List<MemberPlusSendPlanExtEntity>> extMap = getExtMap(memberPlusSendPlanPos);
        // 优惠券权益领取记录
        Map<Integer, List<MemberPlusCouponRecordEntity>> couponRecordMap = getCouponRecordMap(
                memberPlusSendPlanPos);
        // 条件
        return memberPlusSendPlanPos.stream().map(memberPlusSendPlanPo -> {
            Integer id = memberPlusSendPlanPo.getId();
            MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberPlusSendPlanEntity2(
                    memberPlusSendPlanPo, conditionMap.get(id), getExtMapByPlanId(extMap, id),
                    getRecordEntity(couponRecordMap, id));
            // 获取开卡礼领券信息
            if (Objects.equals(memberPlusSendPlanPo.getModelId(), PlusModelEnum.KKL.getModelId())) {
                MemberCouponEntity memberCoupon = memberPlusCouponRepository.getByCouponId(
                        memberPlusSendPlanPo.getProgramId(), memberPlusSendPlanPo.getModelId(),
                        Integer.parseInt(memberPlusSendPlanPo.getProfitValue()),
                        memberPlusSendPlanPo.getOrderSn());
                if (memberCoupon != null) {
                    memberPlusSendPlanEntity.setReceiveValue(memberCoupon.getCouponUserId());
                }
            }
            return memberPlusSendPlanEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 按查询条件查询发放计划
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/2/29 14:43
     */
    @Override
    public List<MemberPlusSendPlanEntity> querySendPlanList(MemberPlusProfitSendPlanQueryReq req) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.querySendPlanList(
                req);
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        return processSendPlanList(memberPlusSendPlanPos);
    }

    @Override
    public List<ExpireMemberPlusSendPlanEntity> listExpireByOrderSn(String orderSn) {
        List<ExpireMemberPlusSendPlanPo> memberPlusSendPlanPos = expireMemberPlusSendPlanMapper.listByOrderSn(
                orderSn);
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        Map<Integer, List<ExpireMemberPlusSendPlanConditionEntity>> conditionMap = getExpireConditionMap(
                memberPlusSendPlanPos);
        // 条件
        return memberPlusSendPlanPos.stream()
                .map(memberPlusSendPlanPo -> converter.toExpireMemberPlusSendPlanEntity(
                        memberPlusSendPlanPo, conditionMap.get(memberPlusSendPlanPo.getId())))
                .collect(Collectors.toList());
    }

    /**
     * 获取会员订单的权益发放计划条件
     */
    @Override
    public List<MemberPlusSendPlanConditionEntity> listConditionsByOrderSnAndModelId(
            List<String> orderSn, Integer modelId, Integer sendStatus, String conditionField) {
        // 1. 查发放计划
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listByOrderSnAndSendStatus(
                orderSn, modelId, sendStatus);
        // 2. 查达成条件
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    @Override
    public List<MemberPlusSendPlanConditionEntity> listDmdsConditionsByOrderSnAndModelId(
            List<String> orderSn, Integer modelId, Integer sendStatus, String conditionField) {
        // 多买多送：取达成中,达成笔数最小的第一条发放计划
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listCurrentDmdsByOrderSnAndSendStatus(
                orderSn, modelId, sendStatus);
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    @Override
    public List<MemberPlusSendPlanConditionEntity> listJjpConditionsByOrderSnAndModelId(
            List<String> orderSn, Integer modelId, Integer sendStatus, String conditionField,
            Integer rejectType) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listCurrentJjpByOrderSnAndSendStatus(
                orderSn, modelId, sendStatus, rejectType);
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    @Override
    public List<MemberPlusSendPlanConditionEntity> listGwConditionsByOrderSnAndModelId(
            List<String> orderSn, Integer modelId, Integer sendStatus, String conditionField) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listGwByOrderSnAndSendStatus(
                orderSn, modelId, sendStatus);
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    @Override
    public List<MemberPlusSendPlanConditionEntity> listByPlanIdsAndState(List<Integer> sendPlanIds,
            Integer sendStatus, String conditionField) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listByPlanIds(
                sendPlanIds, sendStatus);
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    /**
     * 更新发放计划的条件
     */
    @Override
    public void updateCondition(MemberPlusSendPlanConditionEntity conditionEntity) {
        MemberPlusSendPlanConditionPo conditionPo = converter.toMemberPlusSendPlanConditionPo(
                conditionEntity, conditionEntity.getMemberPlusSendPlanId());
        memberPlusSendPlanConditionMapper.updateMemberPlusSendPlanCondition(conditionPo);
    }

    /**
     * 批量查询发放计划
     */
    @Override
    public List<MemberPlusSendPlanEntity> listByIds(List<Integer> ids) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listByIds(ids);
        // 达成条件
        Map<Integer, List<MemberPlusSendPlanConditionEntity>> conditionMap = getConditionMap(
                memberPlusSendPlanPos);
        // 扩展信息
        Map<Integer, List<MemberPlusSendPlanExtEntity>> extMap = getExtMap(memberPlusSendPlanPos);
        // 条件
        return memberPlusSendPlanPos.stream()
                .map(memberPlusSendPlanPo -> converter.toMemberPlusSendPlanEntity(
                        memberPlusSendPlanPo, conditionMap.get(memberPlusSendPlanPo.getId()),
                        getExtMapByPlanId(extMap, memberPlusSendPlanPo.getId())))
                .collect(Collectors.toList());
    }

    @Override
    public MemberPlusSendPlanEntity getById(Integer id) {
        MemberPlusSendPlanPo memberPlusSendPlanPo = memberPlusSendPlanMapper.loadMemberPlusSendPlan(
                id);
        MemberPlusSendPlanExtPo extPo = memberPlusSendPlanExtMapper.getBySendPlanId(id);
        List<MemberPlusSendPlanConditionPo> conditionPos = memberPlusSendPlanConditionMapper.listBySendPlanId(
                id);
        return converter.toMemberPlusSendPlanEntity(memberPlusSendPlanPo, extPo, conditionPos);
    }

    @Override
    public void delSendPlan(String plusOrderSn, Integer modelId) {
        List<MemberPlusSendPlanPo> list = memberPlusSendPlanMapper.listByOrderSnAndModelId(
                plusOrderSn, modelId);
        if (CollectionUtils.isEmpty(list)) {
            log.info("删除发放计划数据为空：{},{}", plusOrderSn, modelId);
            return;
        }
        delSendPlanInfo(list, "移动订单周期失效");
    }

    /**
     * 删除发放计划相关信息
     */
    private void delSendPlanInfo(List<MemberPlusSendPlanPo> list, String remark) {
        List<Integer> ids = list.stream().map(MemberPlusSendPlanPo::getId)
                .collect(Collectors.toList());
        log.info("删除发放计划开始：{}", ids);
        // 20240725 ltq 会员任务处理效率优化,发放计划迁移至失效表
        List<List<MemberPlusSendPlanPo>> sendPlanPartitions = ListUtils.partition(list, 1000);
        sendPlanPartitions.forEach(
                partition -> expireMemberPlusSendPlanMapper.batchInsert(partition, remark));
        // 20240725 ltq 会员任务处理效率优化,分批删除发放计划
        List<List<Integer>> partitions = ListUtils.partition(ids, 1000);
        partitions.forEach(partition -> memberPlusSendPlanMapper.deleteByIds(partition));
        log.info("删除发放计划结束：{}", ids);
        List<MemberPlusSendPlanExtPo> extList = memberPlusSendPlanExtMapper.listBySendPlanIds(ids);
        if (!CollectionUtils.isEmpty(extList)) {
            log.info("删除发放计划扩展信息开始：{}", ids);
            // 扩展信息迁移至失效表
            expireMemberPlusSendPlanExtMapper.batchInsert(extList);
            // 删除发放计划扩展信息
            memberPlusSendPlanExtMapper.deleteByIds(
                    extList.stream().map(MemberPlusSendPlanExtPo::getId)
                            .collect(Collectors.toList()));
            log.info("删除发放计划扩展信息结束：{}", ids);
        }
        List<MemberPlusSendPlanConditionPo> conditionList = memberPlusSendPlanConditionMapper.listBySendPlanIds(
                ids);
        if (!CollectionUtils.isEmpty(conditionList)) {
            log.info("删除发放计划条件开始：{}", ids);
            // 达成条件迁移至失效表
            expireMemberPlusSendPlanConditionMapper.batchInsert(conditionList, remark);
            // 删除达成条件信息
            memberPlusSendPlanConditionMapper.deleteByIds(
                    conditionList.stream().map(MemberPlusSendPlanConditionPo::getId)
                            .collect(Collectors.toList()));
            log.info("删除发放计划条件结束：{}", ids);
        }
        // 删除发放计划任务表
        List<MemberPlusSendPlanTaskPo> taskList = memberPlusSendPlanTaskMapper.listBySendPlanIds(
                ids);
        if (!CollectionUtils.isEmpty(taskList)) {
            log.info("删除发放计划任务开始：{}", ids);
            // 发放计划任务迁移至失效表
            expireMemberPlusSendPlanTaskMapper.batchInsert(taskList);
            // 发放计划任务
            memberPlusSendPlanTaskMapper.deleteByIds(
                    taskList.stream().map(MemberPlusSendPlanTaskPo::getId)
                            .collect(Collectors.toList()));
            log.info("删除发放计划任务结束：{}", ids);
        }
    }

    @Override
    public void moveInvalidSendPlan(List<String> orderSnList) {
        try {
            if (CollectionUtils.isEmpty(orderSnList)) {
                return;
            }
            log.info("迁移失效发放计划相关信息开始：{}", orderSnList);
            List<MemberPlusSendPlanPo> list = memberPlusSendPlanMapper.listByOrderSnsExcludeModelIds(
                    orderSnList, Arrays.asList(PlusModelEnum.JQFX.getModelId(),
                            PlusModelEnum.HKFX.getModelId()));
            if (CollectionUtils.isEmpty(list)) {
                log.info("迁移失效发放计划相关信息数据为空：{}", orderSnList);
                return;
            }
            delSendPlanInfo(list, "会员单过期失效");
            log.info("迁移失效发放计划相关信息结束：{}", orderSnList);
        } catch (Exception e) {
            LogUtil.printLog(e, "迁移失效发放计划相关信息异常");
        }
    }

    @Override
    public List<MemberPlusSendPlanEntity> listByUserAndModelId(Integer userId, Integer modelId,
            Integer configId, Integer sendState) {
        MemberPlusProfitSendPlanQueryReq req = new MemberPlusProfitSendPlanQueryReq();
        req.setUserId(userId);
        req.setModelId(modelId);
        req.setConfigId(configId);
        req.setSendStatuses(Collections.singletonList(sendState));
        List<MemberPlusSendPlanPo> list = memberPlusSendPlanMapper.querySendPlanList(req);
        return converter.toMemberPlusSendPlanEntityList(list);
    }

    @Override
    public MemberPlusSendPlanEntity getByIdAndUserId(Integer id, Integer userId) {
        MemberPlusSendPlanPo memberPlusSendPlanPo = memberPlusSendPlanMapper.getMemberPlusSendPlanByUser(
                id, userId);
        MemberPlusSendPlanExtPo extPo = memberPlusSendPlanExtMapper.getBySendPlanId(id);
        List<MemberPlusSendPlanConditionPo> conditionPos = memberPlusSendPlanConditionMapper.listBySendPlanId(
                id);
        return converter.toMemberPlusSendPlanEntity(memberPlusSendPlanPo, extPo, conditionPos);
    }

    /**
     * 获取还款返现会员订单的权益发放计划条件
     */
    @Override
    public List<MemberPlusSendPlanConditionEntity> listHkfxConditions(List<String> orderSn,
            Integer modelId, Integer sendStatus, String conditionField, Integer periods) {
        // 还款返现
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listCurrentHkfx(
                orderSn, modelId, sendStatus, periods);
        return listMemberPlusSendPlanConditionPos(memberPlusSendPlanPos, conditionField);
    }

    /**
     * 获取发放计划的条件
     */
    private Map<Integer, List<MemberPlusSendPlanConditionEntity>> getConditionMap(
            List<MemberPlusSendPlanPo> memberPlusSendPlanPos) {
        List<MemberPlusSendPlanConditionEntity> conditions = listMemberPlusSendPlanConditionPos(
                memberPlusSendPlanPos, null);
        return conditions.stream().collect(
                Collectors.groupingBy(MemberPlusSendPlanConditionEntity::getMemberPlusSendPlanId));
    }

    /**
     * 获取发放计划的扩展信息
     */
    private Map<Integer, List<MemberPlusSendPlanExtEntity>> getExtMap(
            List<MemberPlusSendPlanPo> memberPlusSendPlanPos) {
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        List<Integer> ids = memberPlusSendPlanPos.stream().map(MemberPlusSendPlanPo::getId)
                .collect(Collectors.toList());
        List<MemberPlusSendPlanExtPo> extPos = memberPlusSendPlanExtMapper.listBySendPlanIds(ids);
        if (CollectionUtils.isEmpty(extPos)) {
            return null;
        }
        List<MemberPlusSendPlanExtEntity> entityList = converter.toMemberPlusSendPlanExtEntityList(
                extPos);
        return entityList.stream().collect(
                Collectors.groupingBy(MemberPlusSendPlanExtEntity::getMemberPlusSendPlanId));
    }

    /**
     * 获取发放计划的扩展信息
     */
    private MemberPlusSendPlanExtEntity getExtMapByPlanId(
            Map<Integer, List<MemberPlusSendPlanExtEntity>> extMap, Integer planId) {
        if (MapUtils.isEmpty(extMap)) {
            return null;
        }
        List<MemberPlusSendPlanExtEntity> list = extMap.get(planId);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 获取发放计划的优惠券领取信息
     */
    private Map<Integer, List<MemberPlusCouponRecordEntity>> getCouponRecordMap(
            List<MemberPlusSendPlanPo> memberPlusSendPlanPos) {
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        List<Integer> ids = memberPlusSendPlanPos.stream().map(MemberPlusSendPlanPo::getId)
                .collect(Collectors.toList());
        List<MemberPlusCouponRecordEntity> recordList = memberPlusCouponRepository.listBySendPlanIds(
                ids);
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }
        return recordList.stream()
                .collect(Collectors.groupingBy(MemberPlusCouponRecordEntity::getSendPlanId));
    }

    /**
     * 获取发放计划的券类权益领取信息
     */
    private MemberPlusCouponRecordEntity getRecordEntity(
            Map<Integer, List<MemberPlusCouponRecordEntity>> extMap, Integer planId) {
        if (MapUtils.isEmpty(extMap)) {
            return null;
        }
        List<MemberPlusCouponRecordEntity> list = extMap.get(planId);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 获取失效的发放计划的条件
     */
    private Map<Integer, List<ExpireMemberPlusSendPlanConditionEntity>> getExpireConditionMap(
            List<ExpireMemberPlusSendPlanPo> memberPlusSendPlanPos) {
        List<ExpireMemberPlusSendPlanConditionEntity> conditions = listExpireMemberPlusSendPlanConditionPos(
                memberPlusSendPlanPos, null);
        return conditions.stream().collect(Collectors.groupingBy(
                ExpireMemberPlusSendPlanConditionEntity::getMemberPlusSendPlanId));
    }

    /**
     * 批量获取发放计划的发放条件
     */
    private List<MemberPlusSendPlanConditionEntity> listMemberPlusSendPlanConditionPos(
            List<MemberPlusSendPlanPo> memberPlusSendPlanPos, String conditionField) {
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return Collections.emptyList();
        }
        List<Integer> sendPlanIds = memberPlusSendPlanPos.stream().map(MemberPlusSendPlanPo::getId)
                .collect(Collectors.toList());
        List<MemberPlusSendPlanConditionPo> conditions = memberPlusSendPlanConditionMapper.listByMemberPlusSendPlanIds(
                sendPlanIds, conditionField);
        return converter.toMemberPlusSendPlanConditionEntity(conditions);
    }

    /**
     * 批量获取失效的发放计划的发放条件
     */
    private List<ExpireMemberPlusSendPlanConditionEntity> listExpireMemberPlusSendPlanConditionPos(
            List<ExpireMemberPlusSendPlanPo> memberPlusSendPlanPos, String conditionField) {
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return Collections.emptyList();
        }
        List<Integer> sendPlanIds = memberPlusSendPlanPos.stream()
                .map(ExpireMemberPlusSendPlanPo::getId).collect(Collectors.toList());
        List<ExpireMemberPlusSendPlanConditionPo> conditions = expireMemberPlusSendPlanConditionMapper.listByMemberPlusSendPlanIds(
                sendPlanIds, conditionField);
        return converter.toExpireMemberPlusSendPlanConditionEntity(conditions);
    }

    @Override
    public List<MemberPlusSendPlanEntity> listByOrderSnAndModelIds(String orderSn,
            List<Integer> modelIdList) {
        List<MemberPlusSendPlanPo> memberPlusSendPlanPos = memberPlusSendPlanMapper.listByOrderSnAndModelIds(
                orderSn, modelIdList);
        if (CollectionUtils.isEmpty(memberPlusSendPlanPos)) {
            return null;
        }
        return processSendPlanList(memberPlusSendPlanPos);
    }

}
