package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;

import java.util.List;

/**
 * 会员月卡续费应用服务接口
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
public interface IPlusMonthMemberRenewalApplication {

    /**
     * 定时任务：创建会员月卡续费订单
     * 筛选当天待生成的续费计划，自动创建续费订单
     */
    void createRenewalOrdersBySchedule();

    /**
     * 根据续费计划创建续费订单
     *
     * @param renewalPlan 续费计划实体
     * @return 创建成功标识
     */
    Boolean createRenewalOrder(PlusMonthMemberRenewalPlanEntity renewalPlan);

    /**
     * 批量创建续费订单
     *
     * @param renewalPlans 续费计划列表
     */
    void createRenewalOrders(List<PlusMonthMemberRenewalPlanEntity> renewalPlans);
}
